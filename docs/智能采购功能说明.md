# 智能采购功能说明

## 功能概述

智能采购功能允许用户选择多个产品，系统会自动调用第三方接口获取最优供应商建议，然后按供应商分组自动生成多张采购订单。

## 功能特点

1. **产品选择**: 通过ProductSelectionDialog组件选择待采购的产品和数量
2. **智能建议**: 后端调用第三方接口获取供应商建议
3. **自动分组**: 按供应商自动分组生成多张采购订单
4. **用户友好**: 提供清晰的loading提示和操作反馈

## 使用流程

1. 在采购订单页面点击"智能采购"按钮
2. 在弹出的产品选择对话框中：
   - 搜索并选择需要采购的产品
   - 设置每个产品的采购数量
   - 点击"确定"提交选择
3. 系统自动处理：
   - 显示"正在获取采购建议..."
   - 调用后端接口获取供应商建议
   - 显示"正在生成采购订单..."
   - 按供应商分组生成多张采购订单
4. 显示操作结果，刷新采购订单列表

## 技术实现

### 前端组件
- `ProductSelectionDialog.vue`: 产品选择对话框
- `index.vue`: 采购订单主页面，包含智能采购按钮和处理逻辑

### API接口
- `createSmartPurchaseOrders`: 智能采购主接口
- `getPurchaseChannelSuggestions`: 获取采购渠道建议

### Loading提示风格
遵循项目统一的loading提示风格：
- "正在获取采购建议..."
- "正在生成采购订单..."
- "正在加载列表..."

## 错误处理

1. 产品选择验证：确保选择了产品且数量大于0
2. 接口调用失败：显示具体错误信息
3. 部分成功场景：显示成功和失败的数量及原因

## 权限控制

智能采购功能需要 `erp:purchase-order:create` 权限。

## 后端接口要求

后端需要实现以下接口：

1. `POST /erp/purchase-order/smart-purchase`
   - 接收产品清单
   - 调用第三方接口获取供应商建议
   - 按供应商分组生成采购订单
   - 返回操作结果

2. `POST /erp/purchase-order/get-purchase-channel-suggestions`
   - 接收产品ID列表
   - 返回采购渠道建议
